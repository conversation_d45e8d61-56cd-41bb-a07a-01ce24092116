/**
 * Session Security Hook
 * React hook for client-side session timeout management and security
 */

import { useEffect, useState, useCallback, useRef, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import type { SessionTimeoutInfo } from '@/lib/session-security'

export interface SessionSecurityHook extends SessionSecurityState {
  extendSession: () => Promise<void>
  forceLogout: () => void
  updateActivity: () => void
}

export interface SessionSecurityState {
  isActive: boolean
  timeoutInfo: SessionTimeoutInfo | null
  showWarning: boolean
  lastActivity: Date | null
  isOnline: boolean
}

export interface SessionSecurityOptions {
  // Timeouts in seconds
  idleTimeout?: number
  warningTime?: number
  checkInterval?: number

  // Callbacks
  onTimeoutWarning?: (timeoutInfo: SessionTimeoutInfo) => void
  onTimeout?: (reason: string) => void
  onActivityDetected?: () => void

  // Features
  enableActivityTracking?: boolean
  enableOnlineStatus?: boolean
  enableAutoRefresh?: boolean
}

const DEFAULT_OPTIONS: Required<SessionSecurityOptions> = {
  idleTimeout: 30 * 60, // 30 minutes
  warningTime: 5 * 60, // 5 minutes
  checkInterval: 30, // 30 seconds
  onTimeoutWarning: () => {},
  onTimeout: () => {},
  onActivityDetected: () => {},
  enableActivityTracking: true,
  enableOnlineStatus: true,
  enableAutoRefresh: true,
}

/**
 * Hook for managing session security and timeouts
 */
export function useSessionSecurity(options: SessionSecurityOptions = {}) {
  const mergedOptions = useMemo(
    () => ({ ...DEFAULT_OPTIONS, ...options }),
    [options]
  )
  const { user, session, signOut } = useAuth()
  const router = useRouter()

  const [state, setState] = useState<SessionSecurityState>({
    isActive: !!session,
    timeoutInfo: null,
    showWarning: false,
    lastActivity: new Date(),
    isOnline: true,
  })

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const activityTimerRef = useRef<NodeJS.Timeout>()
  const checkTimerRef = useRef<NodeJS.Timeout>()
  const warningShownRef = useRef(false)
  const lastActivityRef = useRef(new Date())

  /**
   * Update last activity time
   */
  const updateActivity = useCallback(() => {
    const now = new Date()
    lastActivityRef.current = now
    setState(prev => ({ ...prev, lastActivity: now, showWarning: false }))
    warningShownRef.current = false
    mergedOptions.onActivityDetected()
  }, [mergedOptions])

  /**
   * Calculate timeout information
   */
  const calculateTimeoutInfo = useCallback((): SessionTimeoutInfo => {
    if (!session) {
      return {
        timeUntilTimeout: 0,
        isWarning: false,
        warningTime: mergedOptions.warningTime * 1000,
        maxIdleTime: mergedOptions.idleTimeout * 1000,
      }
    }

    const now = Date.now()
    const lastActivity = lastActivityRef.current.getTime()
    const sessionStart =
      new Date(session.expires_at || 0).getTime() - 8 * 60 * 60 * 1000 // Assume 8h max session

    const idleTimeRemaining = Math.max(
      0,
      lastActivity + mergedOptions.idleTimeout * 1000 - now
    )
    const absoluteTimeRemaining = Math.max(
      0,
      sessionStart + 8 * 60 * 60 * 1000 - now
    ) // 8 hours max

    const timeUntilTimeout = Math.min(idleTimeRemaining, absoluteTimeRemaining)
    const isWarning = timeUntilTimeout <= mergedOptions.warningTime * 1000

    return {
      timeUntilTimeout: Math.floor(timeUntilTimeout / 1000),
      isWarning,
      warningTime: mergedOptions.warningTime * 1000,
      maxIdleTime: mergedOptions.idleTimeout * 1000,
    }
  }, [session, mergedOptions])

  /**
   * Handle session timeout
   */
  const handleTimeout = useCallback(
    async (reason: string) => {
      try {
        await signOut()
        mergedOptions.onTimeout(reason)

        // Redirect to login with timeout message
        const loginUrl = new URL('/login', window.location.origin)
        loginUrl.searchParams.set('timeout', reason)
        if (window.location.pathname !== '/login') {
          loginUrl.searchParams.set('redirectTo', window.location.pathname)
        }
        router.replace(loginUrl.toString())
      } catch (error) {
        console.error('Error during timeout logout:', error)
        // Force reload as fallback
        window.location.href = '/login?timeout=error'
      }
    },
    [signOut, router, mergedOptions]
  )

  /**
   * Check session status
   */
  const checkSessionStatus = useCallback(async () => {
    const timeoutInfo = calculateTimeoutInfo()

    setState(prev => ({
      ...prev,
      timeoutInfo,
      showWarning: timeoutInfo.isWarning,
    }))

    // Handle timeout
    if (timeoutInfo.timeUntilTimeout <= 0) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      handleTimeout('timeout')
      return
    }

    // Show warning if needed
    if (timeoutInfo.isWarning && !warningShownRef.current) {
      warningShownRef.current = true
      mergedOptions.onTimeoutWarning(timeoutInfo)
    }

    // Auto-refresh session if enabled and close to expiration
    if (mergedOptions.enableAutoRefresh && timeoutInfo.timeUntilTimeout < 300) {
      // 5 minutes
      try {
        // Trigger a session refresh by making a request
        await fetch('/api/auth/refresh', { method: 'POST' })
      } catch (error) {
        console.warn('Failed to refresh session:', error)
      }
    }
  }, [calculateTimeoutInfo, handleTimeout, mergedOptions])

  /**
   * Activity event handlers
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleUserActivity = useCallback(
    (event: Event) => {
      updateActivity()
    },
    [updateActivity]
  )

  /**
   * Online status handlers
   */
  const handleOnline = useCallback(() => {
    setState(prev => ({ ...prev, isOnline: true }))
    updateActivity() // Reset activity when coming back online
  }, [updateActivity])

  const handleOffline = useCallback(() => {
    setState(prev => ({ ...prev, isOnline: false }))
  }, [])

  /**
   * Manual session extension
   */
  const extendSession = useCallback(async () => {
    updateActivity()
    // Make a keep-alive request to server
    try {
      await fetch('/api/auth/keep-alive', { method: 'POST' })
    } catch (err) {
      console.warn('Keep-alive request failed:', err)
    }
  }, [updateActivity])

  /**
   * Force logout
   */
  const forceLogout = useCallback(() => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    handleTimeout('user_requested')
  }, [handleTimeout])

  // Initialize and cleanup
  useEffect(() => {
    if (!user || !session) {
      setState(prev => ({
        ...prev,
        isActive: false,
        timeoutInfo: null,
        showWarning: false,
      }))
      return
    }

    setState(prev => ({ ...prev, isActive: true }))

    // Set up activity tracking
    if (mergedOptions.enableActivityTracking) {
      const events = [
        'mousedown',
        'mousemove',
        'keypress',
        'scroll',
        'touchstart',
        'click',
      ]
      events.forEach(event => {
        document.addEventListener(event, handleUserActivity, true)
      })

      // Cleanup activity listeners
      return () => {
        events.forEach(event => {
          document.removeEventListener(event, handleUserActivity, true)
        })
      }
    }
  }, [user, session, handleUserActivity, mergedOptions.enableActivityTracking])

  // Set up online/offline tracking
  useEffect(() => {
    if (mergedOptions.enableOnlineStatus) {
      window.addEventListener('online', handleOnline)
      window.addEventListener('offline', handleOffline)

      return () => {
        window.removeEventListener('online', handleOnline)
        window.removeEventListener('offline', handleOffline)
      }
    }
  }, [handleOnline, handleOffline, mergedOptions.enableOnlineStatus])

  // Set up periodic session checking
  useEffect(() => {
    if (!user || !session) return

    // Initial check
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    checkSessionStatus()

    // Set up periodic checks
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    checkTimerRef.current = setInterval(
      checkSessionStatus,
      mergedOptions.checkInterval * 1000
    )

    return () => {
      if (checkTimerRef.current) {
        clearInterval(checkTimerRef.current)
      }
    }
  }, [user, session, checkSessionStatus, mergedOptions.checkInterval])

  return {
    ...state,
    extendSession,
    forceLogout,
    updateActivity,
  }
}

/**
 * Session timeout warning dialog hook
 */
export function useSessionTimeoutDialog() {
  const [showDialog, setShowDialog] = useState(false)
  const [timeoutInfo, setTimeoutInfo] = useState<SessionTimeoutInfo | null>(
    null
  )
  const timeoutRef = useRef<NodeJS.Timeout>()

  const handleTimeoutWarning = useCallback((info: SessionTimeoutInfo) => {
    setTimeoutInfo(info)
    setShowDialog(true)

    // Auto-close dialog and logout when time runs out
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    const remainingTime = info.timeUntilTimeout * 1000
    timeoutRef.current = setTimeout(() => {
      setShowDialog(false)
      // The main hook will handle the logout
    }, remainingTime)
  }, [])

  const extendSession = useCallback(() => {
    setShowDialog(false)
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
  }, [])

  const logoutNow = useCallback(() => {
    setShowDialog(false)
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
  }, [])

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return {
    showDialog,
    timeoutInfo,
    onTimeoutWarning: handleTimeoutWarning,
    extendSession,
    logoutNow,
  }
}

/**
 * Format remaining time for display
 */
export function formatTimeRemaining(seconds: number): string {
  if (seconds <= 0) return '0s'

  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60

  if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`
  }
  return `${remainingSeconds}s`
}
