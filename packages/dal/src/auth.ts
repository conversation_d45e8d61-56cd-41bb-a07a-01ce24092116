import { createBrowserClient } from './client'
import type { AuthChangeEvent, Session } from '@supabase/supabase-js'

/**
 * Sign in with email (magic link)
 */
export async function signInWithEmail(email: string, redirectTo?: string) {
  const client = createBrowserClient()
  const options: { emailRedirectTo?: string } = {}
  if (typeof redirectTo === 'string') {
    options.emailRedirectTo = redirectTo
  }
  const { error } = await client.auth.signInWithOtp({ email, options })

  if (error) throw error
}

/**
 * Sign out current user
 */
export async function signOut() {
  const client = createBrowserClient()

  const { error } = await client.auth.signOut()

  if (error) throw error
}

/**
 * Get current session
 */
export async function getSession() {
  const client = createBrowserClient()

  const {
    data: { session },
    error,
  } = await client.auth.getSession()

  if (error) throw error
  return session
}

/**
 * Get current user
 */
export async function getUser() {
  const client = createBrowserClient()

  const {
    data: { user },
    error,
  } = await client.auth.getUser()

  if (error) throw error
  return user
}

/**
 * Listen to auth state changes
 */
export function onAuthStateChange(
  callback: (event: AuthChangeEvent, session: Session | null) => void
) {
  const client = createBrowserClient()

  return client.auth.onAuthStateChange(callback)
}

/**
 * Refresh current session
 */
export async function refreshSession() {
  const client = createBrowserClient()

  const {
    data: { session },
    error,
  } = await client.auth.refreshSession()

  if (error) throw error
  return session
}
