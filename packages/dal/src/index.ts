// Main exports for the DAL package
// Import from here instead of using raw Supabase client

// Client utilities
export {
  createSupabaseClient,
  createBrowserClient,
  createServerClient,
  supabase
} from './client'
export type { Database, SupabaseClientTyped, ExtendedSupabaseClient } from './client'

// Auth functions
export {
  signInWithEmail,
  signOut,
  getSession,
  getUser,
  onAuthStateChange,
  refreshSession
} from './auth'

// Tenant management
export {
  listUserTenants,
  getTenant,
  createTenant,
  createTenantRPC,
  hasTenanRole,
  grantTenantRole
} from './tenants'

// Entity management
export {
  listUserEntities,
  getEntity,
  createEntity,
  hasEntityRole,
  grantEntityRole,
  listEntityMemberships
} from './entities'

// Invitation management
export {
  createInvite,
  acceptInvite,
  listSentInvites,
  getPendingInvite,
  getPendingInvitesForUser,
  cancelInvite
} from './invites'

// Security events management
export {
  storeSecurityEvent,
  getSecurityEvents,
  getSecurityEventStats,
  detectSuspiciousIPs,
  cleanupOldSecurityEvents,
  getFailedLoginsByIP,
  getRateLimitViolationsByIP,
  shouldBlockIP
} from './security'
export type { 
  SecurityEvent,
  SecurityEventInput,
  SecurityEventType,
  SecurityEventStats,
  SuspiciousIP
} from './security'

// VAT management
export {
  rpcVatPreview,
  rpcVatExportCsv,
  checkVATEnabled,
  createVATDisabledError
} from './vat'

// Legacy exports (for backward compatibility)
export * from './rpc'